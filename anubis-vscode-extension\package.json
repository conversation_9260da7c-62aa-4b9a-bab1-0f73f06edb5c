{"name": "anubis-ai-assistant", "displayName": "Anubis AI Assistant", "description": "An integrated AI assistant for VS Code, powered by Anubis System's MCP and Horus Team for code generation, analysis, and more.", "version": "0.1.0", "engines": {"vscode": "^1.80.0"}, "categories": ["AI", "Programming Languages", "Other"], "keywords": ["anubis", "horus", "ai", "code generation", "code analysis", "automation", "mcp"], "activationEvents": ["onStartupFinished", "onCommand:anubis.generateCode", "onCommand:anubis.analyzeCode", "onCommand:anubis.toggleMcpConnection"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "anubis.generateCode", "title": "Anubis: Generate Code", "category": "Anubis AI"}, {"command": "anubis.analyzeCode", "title": "Anubis: Analyze Code", "category": "Anubis AI"}, {"command": "anubis.toggleMcpConnection", "title": "Anubis: Connect/Disconnect MCP", "category": "Anubis AI"}], "menus": {"editor/context": [{"when": "editor<PERSON><PERSON><PERSON>", "command": "anubis.generateCode", "group": "anubis@1"}, {"when": "editor<PERSON><PERSON><PERSON>", "command": "anubis.analyzeCode", "group": "anubis@2"}]}, "configuration": {"title": "Anubis AI Assistant Configuration", "properties": {"anubisAI.mcpHost": {"type": "string", "default": "localhost", "description": "The hostname or IP address of the Anubis MCP server."}, "anubisAI.mcpPort": {"type": "number", "default": 8080, "description": "The port of the Anubis MCP server."}, "anubisAI.useHttps": {"type": "boolean", "default": false, "description": "Use HTTPS for MCP connection (wss://) instead of HTTP (ws://)."}, "anubisAI.autoConnect": {"type": "boolean", "default": true, "description": "Automatically connect to the Anubis MCP server on startup"}, "anubisAI.analyzeOnSave": {"type": "boolean", "default": false, "description": "Automatically analyze files on save"}}}}, "scripts": {"vscode:prepublish": "npm run package", "compile": "webpack", "watch": "webpack --watch", "package": "webpack --mode production --devtool hidden-source-map", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js"}, "devDependencies": {"@types/glob": "^7.1.4", "@types/mocha": "^9.0.0", "@types/node": "14.x", "@types/vscode": "^1.60.0", "@types/ws": "^8.5.3", "@typescript-eslint/eslint-plugin": "^5.1.0", "@typescript-eslint/parser": "^5.1.0", "@vscode/test-electron": "^1.6.2", "eslint": "^8.1.0", "glob": "^7.1.7", "mocha": "^9.1.3", "ts-loader": "^9.2.5", "typescript": "^4.4.4", "webpack": "^5.52.1", "webpack-cli": "^4.8.0"}, "dependencies": {"ws": "^8.8.1"}}
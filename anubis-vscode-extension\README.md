
# Anubis AI Assistant - VS Code Extension

An integrated AI assistant for VS Code, powered by Anubis System's Master Control Protocol (MCP) and Horus Team for advanced code generation, analysis, and automation.

## 🚀 Features

### Code Generation
- **AI-Powered Code Generation**: Generate code snippets, functions, and classes using natural language prompts
- **PTAH Agent Integration**: Specialized AI agent for development tasks and code creation
- **Context-Aware**: Considers your current file, language, and selection for better results

### Code Analysis
- **Intelligent Code Analysis**: Comprehensive code review and analysis
- **THOTH Agent Integration**: Specialized AI agent for code analysis and optimization
- **Real-time Diagnostics**: Instant feedback with VS Code's diagnostic system
- **Multiple Analysis Types**: Security, performance, bug detection, and best practices

### MCP Integration
- **WebSocket Connection**: Real-time communication with Anubis MCP server
- **Agent Routing**: Automatic routing to specialized Horus Team agents
- **Status Monitoring**: Live connection status in the status bar
- **Auto-reconnection**: Automatic reconnection on connection loss

## 🛠️ Configuration

Configure the extension through VS Code settings (`Ctrl+,` → Extensions → Anubis AI):

```json
{
  "anubisAI.mcpHost": "localhost",
  "anubisAI.mcpPort": 8080,
  "anubisAI.useHttps": false,
  "anubisAI.autoConnect": true,
  "anubisAI.analyzeOnSave": false
}
```

### Settings Description
- `anubisAI.mcpHost`: Hostname or IP address of the Anubis MCP server
- `anubisAI.mcpPort`: Port number of the Anubis MCP server
- `anubisAI.useHttps`: Use secure WebSocket connection (wss://)
- `anubisAI.autoConnect`: Automatically connect to MCP server on startup
- `anubisAI.analyzeOnSave`: Automatically analyze files when saved

## 📋 Commands

Access commands via Command Palette (`Ctrl+Shift+P`):

- **`Anubis: Generate Code`** - Generate code using AI with natural language prompts
- **`Anubis: Analyze Code`** - Analyze selected code or entire file for issues and improvements
- **`Anubis: Connect/Disconnect MCP`** - Toggle connection to Anubis MCP server

### Context Menu
Right-click in any editor to access:
- Generate Code
- Analyze Code

## 🎯 Usage Examples

### Code Generation
1. Place cursor where you want to insert code
2. Run `Anubis: Generate Code` command
3. Enter a description like "Create a function that sorts an array of objects by name"
4. Generated code will be inserted at cursor position

### Code Analysis
1. Select code to analyze (or leave empty to analyze entire file)
2. Run `Anubis: Analyze Code` command
3. View results in VS Code's Problems panel
4. See diagnostics directly in the editor

## 🏗️ Architecture

### Agent Routing
- **PTAH**: Handles code generation, development tasks, and creative coding
- **THOTH**: Handles code analysis, optimization, and quality assessment

### Request/Response Flow
```
VS Code Extension → MCP Client → WebSocket → Anubis MCP Server → Horus Agents
                                                                      ↓
VS Code Diagnostics ← Response Handler ← WebSocket ← MCP Response ← Agent Response
```

## 🔧 Development

### Prerequisites
- Node.js 16+
- VS Code 1.80+
- TypeScript

### Setup
```bash
# Clone the repository
git clone <repository-url>
cd anubis-vscode-extension

# Install dependencies
npm install

# Compile TypeScript
npm run compile

# Run in development mode
npm run watch
```

### Testing
1. Press `F5` to launch Extension Development Host
2. Test commands and functionality
3. Check Debug Console for logs

### Building
```bash
# Build for production
npm run compile

# Package extension
vsce package
```

## 🔌 MCP Server Requirements

The extension requires an Anubis MCP server running with:
- WebSocket support
- Horus Team agents (PTAH, THOTH)
- JSON-RPC style message handling

### Expected Message Format
```typescript
// Request
{
  "requestId": "req_1234567890_abc123",
  "type": "code_generation" | "code_analysis",
  "agent": "PTAH" | "THOTH",
  "payload": { /* request-specific data */ },
  "context": { /* editor context */ }
}

// Response
{
  "requestId": "req_1234567890_abc123",
  "type": "code_generation_response" | "code_analysis_response",
  "status": "success" | "error",
  "payload": { /* response data */ },
  "message": "Optional user message"
}
```

## 🐛 Troubleshooting

### Connection Issues
- Check MCP server is running on configured host/port
- Verify firewall settings
- Check VS Code Developer Tools Console for WebSocket errors

### No Code Generation
- Ensure PTAH agent is available on MCP server
- Check request format and payload structure
- Verify agent routing configuration

### Missing Diagnostics
- Ensure THOTH agent is available on MCP server
- Check VS Code Problems panel is visible
- Verify diagnostic collection is working

## 📄 License

MIT License - see LICENSE file for details

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📞 Support

For issues and questions:
- GitHub Issues: [Create an issue](https://github.com/your-repo/issues)
- Documentation: [Wiki](https://github.com/your-repo/wiki)

---

**Powered by Anubis System & Horus Team** 🔮

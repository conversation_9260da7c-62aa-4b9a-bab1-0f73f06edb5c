
import * as WebSocket from 'ws';
import { EventEmitter } from 'events';
import { McpRequest, McpResponse, McpNotification } from './types';

export type ConnectionState = 'connecting' | 'connected' | 'disconnected' | 'error';

export class McpClient {
    private ws: WebSocket | null = null;
    private url: string;
    private eventEmitter = new EventEmitter();
    private reconnectAttempts = 0;
    private maxReconnectAttempts = 5;
    private reconnectInterval = 2000; // 2 seconds
    private state: ConnectionState = 'disconnected';
    private pendingRequests: Map<string, { resolve: (value: any) => void; reject: (reason: any) => void }> = new Map();
    private requestCounter = 0;

    constructor(url: string) {
        this.url = url;
    }

    public async connect(): Promise<void> {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            return;
        }

        this.setState('connecting');
        this.reconnectAttempts = 0;

        return this.createWebSocketConnection();
    }

    public async disconnect(): Promise<void> {
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }

        this.setState('disconnected');
    }

    public send(message: any): void {
        if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
            throw new Error('WebSocket is not connected');
        }

        this.ws.send(JSON.stringify(message));
    }

    /**
     * Send a request to the MCP server and return a promise that resolves with the response
     * @param method The method to call
     * @param params The parameters to send
     * @returns A promise that resolves with the response
     */
    public async sendRequest<T = any>(method: string, params: any = {}): Promise<T> {
        if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
            throw new Error('WebSocket is not connected');
        }

        const id = String(this.requestCounter++);
        const request: McpRequest = { id, method, params };

        return new Promise<T>((resolve, reject) => {
            this.pendingRequests.set(id, { resolve, reject });
            this.send(request);

            // Add timeout to reject the promise if no response is received
            setTimeout(() => {
                if (this.pendingRequests.has(id)) {
                    this.pendingRequests.delete(id);
                    reject(new Error(`Request ${id} timed out`));
                }
            }, 30000); // 30 seconds timeout
        });
    }

    /**
     * Send a notification to the MCP server (no response expected)
     * @param method The method to call
     * @param params The parameters to send
     */
    public sendNotification(method: string, params: any = {}): void {
        const notification: McpNotification = { method, params };
        this.send(notification);
    }

    public onMessage(callback: (message: any) => void): void {
        this.eventEmitter.on('message', callback);
    }

    public onNotification(method: string, callback: (params: any) => void): void {
        this.eventEmitter.on(`notification:${method}`, callback);
    }

    public onStateChange(callback: (state: ConnectionState) => void): void {
        this.eventEmitter.on('stateChange', callback);
    }

    private createWebSocketConnection(): Promise<void> {
        return new Promise((resolve, reject) => {
            try {
                this.ws = new WebSocket(this.url);

                this.ws.on('open', () => {
                    console.log('Connected to Anubis MCP server');
                    this.setState('connected');
                    this.reconnectAttempts = 0;
                    resolve();
                });

                this.ws.on('message', (data: WebSocket.Data) => {
                    try {
                        const message = JSON.parse(data.toString());
                        this.handleMessage(message);
                    } catch (error) {
                        console.error('Failed to parse message:', error);
                    }
                });

                this.ws.on('close', () => {
                    console.log('Disconnected from Anubis MCP server');
                    this.setState('disconnected');
                    this.attemptReconnect();
                });

                this.ws.on('error', (error: Error) => {
                    console.error('WebSocket error:', error);
                    this.setState('error');
                    reject(error);
                });
            } catch (error) {
                this.setState('error');
                reject(error);
            }
        });
    }

    private handleMessage(message: any): void {
        // Emit the raw message for listeners
        this.eventEmitter.emit('message', message);

        // Handle responses to requests
        if ('id' in message) {
            const id = message.id;
            const pendingRequest = this.pendingRequests.get(id);

            if (pendingRequest) {
                this.pendingRequests.delete(id);

                if ('error' in message) {
                    pendingRequest.reject(message.error);
                } else {
                    pendingRequest.resolve(message.result);
                }
            }
        } 
        // Handle notifications
        else if ('method' in message) {
            this.eventEmitter.emit(`notification:${message.method}`, message.params);
        }
    }

    private attemptReconnect(): void {
        if (this.state === 'disconnected' && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);

            setTimeout(() => {
                this.createWebSocketConnection().catch(error => {
                    console.error('Reconnection attempt failed:', error);
                });
            }, this.reconnectInterval);
        }
    }

    private setState(state: ConnectionState): void {
        this.state = state;
        this.eventEmitter.emit('stateChange', state);
    }
}

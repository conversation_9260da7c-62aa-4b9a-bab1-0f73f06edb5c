
import * as WebSocket from 'ws';
import { McpRe<PERSON>, McpResponse } from './types';

export type ConnectionState = 'connecting' | 'connected' | 'disconnected' | 'error';

export class McpClient {
    private ws: WebSocket | null = null;
    private host: string;
    private port: number;
    private useHttps: boolean;
    private messageCallback: (response: McpResponse) => void;
    private statusChangeCallback: (connected: boolean, message?: string) => void;
    private reconnectInterval: NodeJS.Timeout | null = null;
    private readonly RECONNECT_DELAY_MS = 5000;

    constructor(
        host: string,
        port: number,
        useHttps: boolean,
        messageCallback: (response: McpResponse) => void,
        statusChangeCallback: (connected: boolean, message?: string) => void
    ) {
        this.host = host;
        this.port = port;
        this.useHttps = useHttps;
        this.messageCallback = messageCallback;
        this.statusChangeCallback = statusChangeCallback;
    }

    public connect() {
        if (this.ws && (this.ws.readyState === WebSocket.OPEN || this.ws.readyState === WebSocket.CONNECTING)) {
            console.log('Already connected or connecting to Anubis MCP.');
            return;
        }

        const protocol = this.useHttps ? 'wss' : 'ws';
        const url = `${protocol}://${this.host}:${this.port}`;
        console.log(`Attempting to connect to Anubis MCP at ${url}...`);
        this.statusChangeCallback(false, `Connecting to ${url}...`);

        this.ws = new WebSocket(url);

        this.ws.onopen = () => {
            console.log('Connected to Anubis MCP');
            this.statusChangeCallback(true, `Connected to ${url}`);
            if (this.reconnectInterval) {
                clearInterval(this.reconnectInterval);
                this.reconnectInterval = null;
            }
        };

        this.ws.onmessage = (event) => {
            try {
                const response: McpResponse = JSON.parse(event.data.toString());
                this.messageCallback(response);
            } catch (e) {
                console.error('Failed to parse MCP response:', e);
            }
        };

        this.ws.onerror = (error) => {
            console.error('Anubis MCP connection error:', error);
            this.statusChangeCallback(false, `Connection error`);
            this.scheduleReconnect();
        };

        this.ws.onclose = (event) => {
            console.log('Disconnected from Anubis MCP:', event.code, event.reason);
            this.statusChangeCallback(false, `Disconnected: ${event.reason || 'Closed'}`);
            this.scheduleReconnect();
        };
    }

    private scheduleReconnect() {
        if (!this.reconnectInterval) {
            this.reconnectInterval = setInterval(() => {
                console.log('Attempting to reconnect to Anubis MCP...');
                this.connect();
            }, this.RECONNECT_DELAY_MS);
        }
    }

    public disconnect() {
        if (this.reconnectInterval) {
            clearInterval(this.reconnectInterval);
            this.reconnectInterval = null;
        }
        if (this.ws) {
            this.ws.close();
            this.ws = null;
            this.statusChangeCallback(false, 'Manually disconnected.');
        }
    }

    public isConnected(): boolean {
        return this.ws?.readyState === WebSocket.OPEN;
    }

    public sendRequest(request: McpRequest): void {
        if (this.isConnected()) {
            this.ws?.send(JSON.stringify(request));
            console.log('Sent request to MCP:', request);
        } else {
            console.error('Attempted to send request while disconnected.');
        }
    }

    public dispose() {
        this.disconnect();
    }
}

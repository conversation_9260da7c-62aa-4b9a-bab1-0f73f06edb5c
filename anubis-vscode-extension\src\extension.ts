
import * as vscode from 'vscode';
import { McpClient } from './mcpClient';
import { AnubisStatusBar } from './statusBar';
import { AnubisDiagnostics } from './diagnostics';
import {
    McpRequest,
    McpResponse,
    CodeGenerationRequestPayload,
    CodeAnalysisRequestPayload,
    CodeGenerationResponsePayload,
    CodeAnalysisResponsePayload,
    ErrorResponsePayload
} from './types';

// Global variables for extension state
let mcpClient: McpClient | undefined;
let statusBar: AnubisStatusBar | undefined;
let diagnostics: AnubisDiagnostics | undefined;

export async function activate(context: vscode.ExtensionContext) {
    console.log('Anubis AI Assistant Extension is now active');

    // Initialize status bar
    statusBar = new AnubisStatusBar();
    context.subscriptions.push(statusBar);

    // Initialize diagnostics
    diagnostics = new AnubisDiagnostics();
    context.subscriptions.push(diagnostics);

    // Get extension configuration
    const config = vscode.workspace.getConfiguration('anubisAI');
    const autoConnect = config.get<boolean>('autoConnect', true);

    // Auto-connect if enabled
    if (autoConnect) {
        connectToMcpServer();
    }

    // Helper function to connect to MCP server
    function connectToMcpServer(): void {
        if (mcpClient) {
            vscode.window.showInformationMessage('Anubis MCP client is already running');
            return;
        }

        const config = vscode.workspace.getConfiguration('anubisAI');
        const host = config.get<string>('mcpHost', 'localhost');
        const port = config.get<number>('mcpPort', 8080);
        const useHttps = config.get<boolean>('useHttps', false);

        // Create and start the MCP client
        mcpClient = new McpClient(
            host,
            port,
            useHttps,
            handleMcpResponse,
            handleStatusChange
        );

        mcpClient.connect();
    }

    // Handle MCP responses
    function handleMcpResponse(response: McpResponse): void {
        console.log('Received MCP response:', response);

        if (response.status === 'error') {
            const errorPayload = response.payload as ErrorResponsePayload;
            vscode.window.showErrorMessage(`Anubis Error: ${errorPayload.message}`);
            return;
        }

        switch (response.type) {
            case 'code_generation_response':
                handleCodeGenerationResponse(response);
                break;
            case 'code_analysis_response':
                handleCodeAnalysisResponse(response);
                break;
            default:
                console.log('Unknown response type:', response.type);
        }
    }

    // Handle status changes
    function handleStatusChange(connected: boolean, message?: string): void {
        if (statusBar) {
            statusBar.updateStatus(connected, message);
        }
    }

    // Handle code generation response
    function handleCodeGenerationResponse(response: McpResponse): void {
        const payload = response.payload as CodeGenerationResponsePayload;

        // Insert generated code at cursor position
        const editor = vscode.window.activeTextEditor;
        if (editor) {
            editor.edit(editBuilder => {
                editBuilder.insert(editor.selection.active, payload.generatedCode);
            });

            if (payload.explanation) {
                vscode.window.showInformationMessage(`Code generated: ${payload.explanation}`);
            }
        }
    }

    // Handle code analysis response
    function handleCodeAnalysisResponse(response: McpResponse): void {
        const payload = response.payload as CodeAnalysisResponsePayload;
        const editor = vscode.window.activeTextEditor;

        if (editor && diagnostics) {
            diagnostics.updateDiagnostics(editor.document.uri.fsPath, payload);

            if (payload.summary) {
                vscode.window.showInformationMessage(`Analysis complete: ${payload.summary}`);
            }
        }
    }

    // Register commands
    const generateCodeCommand = vscode.commands.registerCommand('anubis.generateCode', async () => {
        if (!mcpClient || !mcpClient.isConnected()) {
            vscode.window.showErrorMessage('Anubis MCP client is not connected. Please check your connection.');
            return;
        }

        const prompt = await vscode.window.showInputBox({
            prompt: 'Enter a description of the code you want to generate',
            placeHolder: 'e.g., Create a function that sorts an array'
        });

        if (!prompt) {
            return;
        }

        const editor = vscode.window.activeTextEditor;
        const language = editor?.document.languageId;

        const request: McpRequest = {
            requestId: generateRequestId(),
            type: 'code_generation',
            agent: 'PTAH', // Route to PTAH for code generation
            payload: {
                prompt,
                targetLanguage: language,
                additionalContext: editor?.document.getText()
            } as CodeGenerationRequestPayload,
            context: {
                editorLanguage: language,
                editorFilePath: editor?.document.uri.fsPath,
                editorSelection: editor?.document.getText(editor.selection)
            }
        };

        mcpClient.sendRequest(request);
        vscode.window.showInformationMessage('Generating code...');
    });

    const analyzeCodeCommand = vscode.commands.registerCommand('anubis.analyzeCode', async () => {
        if (!mcpClient || !mcpClient.isConnected()) {
            vscode.window.showErrorMessage('Anubis MCP client is not connected. Please check your connection.');
            return;
        }

        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showWarningMessage('No active text editor found');
            return;
        }

        const selectedText = editor.document.getText(editor.selection);
        const codeToAnalyze = selectedText || editor.document.getText();

        if (!codeToAnalyze.trim()) {
            vscode.window.showWarningMessage('No code to analyze');
            return;
        }

        const request: McpRequest = {
            requestId: generateRequestId(),
            type: 'code_analysis',
            agent: 'THOTH', // Route to THOTH for code analysis
            payload: {
                code: codeToAnalyze,
                language: editor.document.languageId,
                analysisType: 'best_practices'
            } as CodeAnalysisRequestPayload,
            context: {
                editorLanguage: editor.document.languageId,
                editorFilePath: editor.document.uri.fsPath,
                editorSelection: selectedText
            }
        };

        mcpClient.sendRequest(request);
        vscode.window.showInformationMessage('Analyzing code...');
    });

    const toggleConnectionCommand = vscode.commands.registerCommand('anubis.toggleMcpConnection', () => {
        if (mcpClient && mcpClient.isConnected()) {
            mcpClient.disconnect();
            mcpClient = undefined;
            vscode.window.showInformationMessage('Disconnected from Anubis MCP');
        } else {
            connectToMcpServer();
            vscode.window.showInformationMessage('Connecting to Anubis MCP...');
        }
    });

    // Helper function to generate unique request IDs
    function generateRequestId(): string {
        return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    context.subscriptions.push(generateCodeCommand, analyzeCodeCommand, toggleConnectionCommand);
}

export function deactivate() {
    if (mcpClient) {
        mcpClient.dispose();
        mcpClient = undefined;
    }

    if (statusBar) {
        statusBar.dispose();
        statusBar = undefined;
    }

    if (diagnostics) {
        diagnostics.dispose();
        diagnostics = undefined;
    }
}

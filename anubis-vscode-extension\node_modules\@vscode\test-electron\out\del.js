"use strict";
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
Object.defineProperty(exports, "__esModule", { value: true });
exports.rmdir = void 0;
const rimraf = require("rimraf");
// todo: rmdir supports a `recurse` option as of Node 12. Drop rimraf when 10 is EOL.
function rmdir(dir) {
    return new Promise((c, e) => {
        rimraf(dir, err => {
            if (err) {
                return e(err);
            }
            c();
        });
    });
}
exports.rmdir = rmdir;

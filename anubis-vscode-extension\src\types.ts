
/**
 * Common structure for all MCP requests.
 */
export interface McpRequest {
    requestId: string;
    type: string; // e.g., "code_generation", "code_analysis"
    agent?: string; // e.g., "PTAH", "THOTH" for Horus agents
    payload: any;
    context?: {
        // Additional context about the request source (e.g., VS Code editor state)
        editorSelection?: string;
        editorLanguage?: string;
        editorFilePath?: string;
        currentLineContent?: string;
    };
}

/**
 * Payload for a code generation request.
 */
export interface CodeGenerationRequestPayload {
    prompt: string;
    targetLanguage?: string;
    additionalContext?: string;
}

/**
 * Payload for a code analysis request.
 */
export interface CodeAnalysisRequestPayload {
    code: string;
    language?: string;
    analysisType?: 'security' | 'performance' | 'bug_detection' | 'best_practices';
}

/**
 * Common structure for all MCP responses.
 */
export interface McpResponse {
    requestId: string;
    type: string; // e.g., "code_generation_response", "code_analysis_response", "error"
    status: 'success' | 'error' | 'in_progress';
    payload: any;
    message?: string; // User-friendly message
}

/**
 * Payload for a code generation response.
 */
export interface CodeGenerationResponsePayload {
    generatedCode: string;
    explanation?: string;
    suggestions?: string[];
}

/**
 * Payload for a code analysis response.
 */
export interface CodeAnalysisResponsePayload {
    analysisResults: {
        message: string;
        severity: 'info' | 'warning' | 'error';
        lineNumber: number; // 0-indexed
        columnNumber: number; // 0-indexed
    }[];
    summary?: string;
    recommendations?: string;
}

/**
 * Payload for an error response.
 */
export interface ErrorResponsePayload {
    code: string;
    message: string;
    details?: string;
}

/**
 * Interface for MCP notification message
 */
export interface McpNotification {
    method: string;
    params: any;
}

/**
 * Interface for diagnostic information (legacy support)
 */
export interface Diagnostic {
    file: string;
    range: {
        start: { line: number; character: number };
        end: { line: number; character: number };
    };
    severity: 'error' | 'warning' | 'information' | 'hint';
    message: string;
    source: string;
    code?: string | number;
}

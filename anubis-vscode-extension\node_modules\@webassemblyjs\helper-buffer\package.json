{"name": "@webassemblyjs/helper-buffer", "version": "1.14.1", "description": "Buffer manipulation utility", "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/xtuc/webassemblyjs.git"}, "publishConfig": {"access": "public"}, "author": "<PERSON>", "license": "MIT", "devDependencies": {"@webassemblyjs/wasm-parser": "1.14.1", "jest-diff": "^24.0.0"}, "gitHead": "25d52b1296e151ac56244a7c3886661e6b4a69ea"}
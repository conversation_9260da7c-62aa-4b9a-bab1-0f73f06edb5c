
import * as vscode from 'vscode';
import { CodeAnalysisResponsePayload } from './types';

export class AnubisDiagnostics {
    private diagnosticCollection: vscode.DiagnosticCollection;

    constructor() {
        this.diagnosticCollection = vscode.languages.createDiagnosticCollection('anubis-ai');
    }

    public updateDiagnostics(filePath: string, analysisResults: CodeAnalysisResponsePayload): void {
        const uri = vscode.Uri.file(filePath);
        const diagnostics: vscode.Diagnostic[] = [];

        for (const result of analysisResults.analysisResults) {
            const range = new vscode.Range(
                result.lineNumber,
                result.columnNumber,
                result.lineNumber,
                result.columnNumber + 1 // Highlight at least one character
            );

            const severity = this.mapSeverity(result.severity);
            const diagnostic = new vscode.Diagnostic(range, result.message, severity);
            diagnostic.source = 'Anubis AI';
            diagnostics.push(diagnostic);
        }

        this.diagnosticCollection.set(uri, diagnostics);
    }

    private mapSeverity(severity: 'info' | 'warning' | 'error'): vscode.DiagnosticSeverity {
        switch (severity) {
            case 'error':
                return vscode.DiagnosticSeverity.Error;
            case 'warning':
                return vscode.DiagnosticSeverity.Warning;
            case 'info':
                return vscode.DiagnosticSeverity.Information;
            default:
                return vscode.DiagnosticSeverity.Information;
        }
    }

    public clearDiagnostics(): void {
        this.diagnosticCollection.clear();
    }

    public dispose(): void {
        this.diagnosticCollection.dispose();
    }
}

{"name": "@vscode/test-electron", "version": "1.6.2", "scripts": {"compile": "tsc -p ./", "watch": "tsc -w -p ./", "prepublish": "tsc -p ./", "test": "eslint lib --ext ts && tsc --noEmit"}, "main": "./out/index.js", "engines": {"node": ">=8.9.3"}, "dependencies": {"http-proxy-agent": "^4.0.1", "https-proxy-agent": "^5.0.0", "rimraf": "^3.0.2", "unzipper": "^0.10.11"}, "devDependencies": {"@types/node": "^12", "@types/rimraf": "^3.0.0", "@types/unzipper": "^0.10.3", "@typescript-eslint/eslint-plugin": "^4.13.0", "@typescript-eslint/parser": "^4.13.0", "eslint": "^7.17.0", "eslint-plugin-header": "^3.1.0", "typescript": "^4.3.5"}, "license": "MIT", "author": "Visual Studio Code Team", "repository": {"type": "git", "url": "https://github.com/Microsoft/vscode-test.git"}, "bugs": {"url": "https://github.com/Microsoft/vscode-test/issues"}}
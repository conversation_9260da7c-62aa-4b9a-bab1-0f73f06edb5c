
# Change Log

All notable changes to the "Anubis AI Assistant" extension will be documented in this file.

## [0.1.0] - 2024-07-25

### Added
- **Complete Rewrite**: Rebuilt extension to match Anubis AI Assistant specifications
- **Code Generation**: AI-powered code generation with PTAH agent integration
- **Code Analysis**: Intelligent code analysis with THOTH agent integration
- **Enhanced MCP Client**: Improved WebSocket client with auto-reconnection
- **Agent Routing**: Automatic routing to specialized Horus Team agents
- **Context-Aware Requests**: Include editor context in all requests
- **Real-time Diagnostics**: Integration with VS Code's diagnostic system
- **Status Bar Integration**: Live connection status monitoring
- **Configuration Options**: Comprehensive settings for MCP connection
- **Command Palette Integration**: Easy access to all features
- **Context Menu Integration**: Right-click access to generate and analyze commands

### Changed
- **Package Name**: Changed from `anubis-vscode-extension` to `anubis-ai-assistant`
- **Display Name**: Updated to "Anubis AI Assistant"
- **Configuration Namespace**: Changed from `anubis` to `anubisAI`
- **Command Names**: Updated to use `anubis.*` prefix
- **Message Format**: Implemented new request/response structure
- **TypeScript Interfaces**: Complete rewrite of type definitions
- **Status Bar**: Enhanced with better visual indicators
- **Diagnostics**: Improved integration with VS Code's Problems panel

### Technical Improvements
- **TypeScript**: Enhanced type safety with comprehensive interfaces
- **Error Handling**: Improved error handling and user feedback
- **Code Organization**: Better separation of concerns
- **Documentation**: Comprehensive README and inline documentation
- **Build Process**: Optimized webpack configuration

### Bug Fixes
- **JSON Parsing**: Fixed regex escape characters in package.json
- **TypeScript Compilation**: Resolved unused variable warnings
- **ESLint Configuration**: Added proper linting rules
- **WebSocket Connection**: Improved connection stability

"use strict";
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
Object.defineProperty(exports, "__esModule", { value: true });
exports.getJSON = exports.getStream = void 0;
const https = require("https");
const util_1 = require("./util");
async function getStream(api) {
    return new Promise((resolve, reject) => {
        https.get(api, util_1.urlToOptions(api), res => resolve(res)).on('error', reject);
    });
}
exports.getStream = getStream;
async function getJSON(api) {
    return new Promise((resolve, reject) => {
        https.get(api, util_1.urlToOptions(api), res => {
            if (res.statusCode !== 200) {
                reject('Failed to get JSON');
            }
            let data = '';
            res.on('data', chunk => {
                data += chunk;
            });
            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(data);
                    resolve(jsonData);
                }
                catch (err) {
                    console.error(`Failed to parse response from ${api} as JSON`);
                    reject(err);
                }
            });
            res.on('error', err => {
                reject(err);
            });
        });
    });
}
exports.getJSON = getJSON;

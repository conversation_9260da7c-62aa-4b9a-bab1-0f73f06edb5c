
import * as vscode from 'vscode';
import { ConnectionState } from './mcpClient';

export class StatusBar implements vscode.Disposable {
    private statusBarItem: vscode.StatusBarItem;

    constructor() {
        this.statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Left, 100);
        this.updateState('disconnected');
        this.statusBarItem.show();
    }

    public updateState(state: ConnectionState): void {
        switch (state) {
            case 'connecting':
                this.statusBarItem.text = '$(sync~spin) Anubis: Connecting...';
                this.statusBarItem.tooltip = 'Connecting to Anubis MCP server';
                this.statusBarItem.backgroundColor = new vscode.ThemeColor('statusBarItem.warningBackground');
                break;
            case 'connected':
                this.statusBarItem.text = '$(check) Anubis: Connected';
                this.statusBarItem.tooltip = 'Connected to Anubis MCP server';
                this.statusBarItem.backgroundColor = new vscode.ThemeColor('statusBarItem.prominentBackground');
                break;
            case 'disconnected':
                this.statusBarItem.text = '$(circle-slash) Anubis: Disconnected';
                this.statusBarItem.tooltip = 'Disconnected from Anubis MCP server';
                this.statusBarItem.backgroundColor = undefined;
                break;
            case 'error':
                this.statusBarItem.text = '$(error) Anubis: Error';
                this.statusBarItem.tooltip = 'Error connecting to Anubis MCP server';
                this.statusBarItem.backgroundColor = new vscode.ThemeColor('statusBarItem.errorBackground');
                break;
        }
    }

    public dispose(): void {
        this.statusBarItem.dispose();
    }
}

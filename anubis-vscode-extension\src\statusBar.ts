
import * as vscode from 'vscode';

export class AnubisStatusBar {
    private statusBarItem: vscode.StatusBarItem;

    constructor() {
        this.statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
        this.updateStatus(false); // Initial status: disconnected
        this.statusBarItem.show();
    }

    public updateStatus(connected: boolean, message?: string) {
        if (connected) {
            this.statusBarItem.text = `$(check) Anubis MCP`;
            this.statusBarItem.tooltip = message || 'Connected to Anubis Master Control Protocol';
            this.statusBarItem.backgroundColor = undefined; // Default green
        } else {
            this.statusBarItem.text = `$(close) Anubis MCP`;
            this.statusBarItem.tooltip = message || 'Disconnected from Anubis Master Control Protocol';
            this.statusBarItem.backgroundColor = new vscode.ThemeColor('statusBarItem.errorBackground');
        }
    }

    public dispose() {
        this.statusBarItem.dispose();
    }
}
